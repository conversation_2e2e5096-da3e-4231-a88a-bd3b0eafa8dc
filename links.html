<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ma</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            min-height: 100vh;
            background: linear-gradient(135deg, #f97316 0%, #fb923c 25%, #fdba74 50%, #fed7aa 75%, #ffffff 100%);
            overflow-x: hidden;
        }

        .container-custom {
            max-width: 448px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }

        .profile-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .profile-image-container {
            position: relative;
            display: inline-block;
            margin-bottom: 1.5rem;
        }

        .profile-image {
            width: 96px;
            height: 96px;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            object-fit: cover;
        }

        .online-indicator {
            position: absolute;
            bottom: -8px;
            right: -8px;
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #fb923c, #f97316);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .online-dot {
            width: 12px;
            height: 12px;
            background-color: #9a3412;
            border-radius: 50%;
        }

        .username {
            font-size: 1.5rem;
            font-weight: 700;
            color: #9a3412;
            margin-bottom: 0.5rem;
        }

        .description {
            color: #c2410c;
            font-size: 0.875rem;
            line-height: 1.5;
            max-width: 320px;
            margin: 0 auto;
        }

        .share-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .share-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1.5rem;
            background: rgba(154, 52, 18, 0.2);
            backdrop-filter: blur(8px);
            color: #9a3412;
            border: none;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .share-button:hover {
            background: rgba(154, 52, 18, 0.3);
            color: #9a3412;
            text-decoration: none;
        }

        .links-section {
            margin-bottom: 3rem;
        }

        .link-button {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #ffffff, #fed7aa);
            border: 1px solid #fdba74;
            border-radius: 1rem;
            margin-bottom: 1rem;
            text-decoration: none;
            color: inherit;
            display: block;
            transition: all 0.3s ease;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .link-button:hover {
            background: linear-gradient(135deg, #fed7aa, #fdba74);
            transform: scale(1.05);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            text-decoration: none;
            color: inherit;
        }

        .link-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .link-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #f97316, #ea580c);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.3s ease;
        }

        .link-button:hover .link-icon {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .link-text {
            flex: 1;
            text-align: left;
        }

        .link-title {
            font-weight: 600;
            color: #9a3412;
            font-size: 1.125rem;
            margin-bottom: 0.25rem;
        }

        .link-description {
            color: #c2410c;
            font-size: 0.875rem;
        }

        .link-arrow {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #f97316, #ea580c);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .link-button:hover .link-arrow {
            transform: scale(1.1);
        }

        .arrow-dot {
            width: 8px;
            height: 8px;
            background-color: white;
            border-radius: 50%;
        }

        .footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(154, 52, 18, 0.2);
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 1rem;
        }

        .footer-link {
            color: #c2410c;
            text-decoration: none;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            transition: color 0.2s ease;
        }

        .footer-link:hover {
            color: #9a3412;
            text-decoration: none;
        }

        .footer-link i {
            font-size: 0.75rem;
        }

        .copyright {
            text-align: center;
            margin-top: 1rem;
            color: #ea580c;
            font-size: 0.75rem;
        }

        @media (max-width: 768px) {
            .container-custom {
                padding: 1.5rem 1rem;
            }
            
            .footer-links {
                flex-direction: column;
                align-items: center;
                gap: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-custom">
        <!-- Profile Section -->
        <div class="profile-section">
            <div class="profile-image-container">
                <img src="LOGO.jpg"
                     alt="Profile" class="profile-image">
                <div class="online-indicator">
                    <div class="online-dot"></div>
                </div>
            </div>
            
            <h1 class="username">Match Creativo</h1>
            
            <p class="description">
                Links de Match Creativo 
            </p>
        </div>

        <!-- Share Button -->
        <div class="share-section">
            <button class="share-button" onclick="sharePage()">
                <i class="fas fa-share-alt"></i>
                Compartir página
            </button>
        </div>

        <!-- Links Section -->
        <div class="links-section">
            <a href="https://chat.whatsapp.com/HVbGMfrAFrG2SmxDFP0LiU" class="link-button" target="_blank" rel="noopener noreferrer">
                <div class="link-content">
                    <div class="link-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="link-text">
                        <div class="link-title">WhatsApp</div>
                        <div class="link-description">Message me directly</div>
                    </div>
                    <div class="link-arrow">
                        <div class="arrow-dot"></div>
                    </div>
                </div>
            </a>

            <a href="https://demo.matchcreativo.cl/" class="link-button" target="_blank" rel="noopener noreferrer">
                <div class="link-content">
                    <div class="link-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="link-text">
                        <div class="link-title">Demo Match Creativo</div>
                        <div class="link-description">Visita la pagina demo </div>
                    </div>
                    <div class="link-arrow">
                        <div class="arrow-dot"></div>
                    </div>
                </div>
            </a>
            <a href="https://test.matchcreativo.cl/" class="link-button" target="_blank" rel="noopener noreferrer">
                <div class="link-content">
                    <div class="link-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="link-text">
                        <div class="link-title">Test Match Creativo</div>
                        <div class="link-description">Visita La pagina de test </div>
                    </div>
                    <div class="link-arrow">
                        <div class="arrow-dot"></div>
                    </div>
                </div>
            </a>

            <a href="https://docs.google.com/forms/d/e/1FAIpQLSdXpdwYTalr3UMSuOMLjRcgFpQPihwbnJhPi1qAe19JkiEtBg/viewform?usp=header" class="link-button" target="_blank" rel="noopener noreferrer">
                <div class="link-content">
                    <div class="link-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="link-text">
                        <div class="link-title">Formulario de Registro</div>
                        <div class="link-description">Regístrate aquí en match creativo</div>
                    </div>
                    <div class="link-arrow">
                        <div class="arrow-dot"></div>
                    </div>
                </div>
            </a>

            <a href="https://docs.google.com/forms/d/e/1FAIpQLSfRWUJe-ZgHNVkxpND5sd3kNG6zpzAKubEZc7zPWVkw8OBLXQ/viewform?usp=header" class="link-button" target="_blank" rel="noopener noreferrer">
                <div class="link-content">
                    <div class="link-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="link-text">
                        <div class="link-title">Formulario de Poryectos y Noticias</div>
                        <div class="link-description">Comparte tus proyectos y noticias</div>
                    </div>
                    <div class="link-arrow">
                        <div class="arrow-dot"></div>
                    </div>
                </div>
            </a>
        </div>
          <a href="https://docs.google.com/forms/d/e/1FAIpQLSfaZaPVPLuYtWAktE1vUNFXnJnpFbUzrEuFSr9TAqvRLkm1fg/viewform?usp=header" class="link-button" target="_blank" rel="noopener noreferrer">
                <div class="link-content">
                    <div class="link-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="link-text">
                        <div class="link-title">Formulario de Feedback</div>
                        <div class="link-description">Dejanos tu Feedback aca despues de que hayas tenido un match</div>
                    </div>
                    <div class="link-arrow">
                        <div class="arrow-dot"></div>
                    </div>
                </div>
            </a>
        </div>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function sharePage() {
            if (navigator.share) {
                navigator.share({
                    title: 'My Links',
                    text: 'Check out all my social media and platforms!',
                    url: window.location.href
                }).catch(err => console.log('Error sharing:', err));
            } else {
                // Fallback to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Link copied to clipboard!');
                }).catch(() => {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = window.location.href;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('Link copied to clipboard!');
                });
            }
        }
    </script>
</body>
</html>